FROM node:18-alpine

# 設定工作目錄
WORKDIR /app

# 複製 package.json 和 package-lock.json
COPY package*.json ./

# 安裝依賴套件
RUN npm ci --only=production && npm cache clean --force

# 複製應用程式源代碼
COPY . .

# 建立非 root 使用者
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# 變更檔案擁有者
RUN chown -R nextjs:nodejs /app
USER nextjs

# 暴露應用程式端口
EXPOSE 3000

# 設定環境變數
ENV NODE_ENV=production

# 啟動應用程式
CMD ["npm", "start"]
